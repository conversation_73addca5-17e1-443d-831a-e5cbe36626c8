[debug] [2025-08-02T03:07:51.595Z] ----------------------------------------------------------------------
[debug] [2025-08-02T03:07:51.601Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\shop-bze\my-app\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only auth --project demo-project --export-on-exit=./data/firebase-emulator --import=./data/firebase-emulator
[debug] [2025-08-02T03:07:51.602Z] CLI Version:   13.35.1
[debug] [2025-08-02T03:07:51.602Z] Platform:      win32
[debug] [2025-08-02T03:07:51.602Z] Node Version:  v24.4.1
[debug] [2025-08-02T03:07:51.602Z] Time:          Fri Aug 01 2025 23:07:51 GMT-0400 (Eastern Daylight Time)
[debug] [2025-08-02T03:07:51.603Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-08-02T03:07:52.128Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] Failed to authenticate, have you run firebase login?
[warn] !  emulators: You are not currently authenticated so some features may not work correctly. Please run firebase login to authenticate the CLI. 
[info] i  emulators: Starting emulators: auth {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth"}}
[info] i  emulators: Detected demo project ID "demo-project", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail. {"metadata":{"emulator":{"name":"hub"},"message":"Detected demo project ID \"demo-project\", emulated services will use a demo configuration and attempts to access non-emulated services for this project will fail."}}
[debug] [2025-08-02T03:07:52.299Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-02T03:07:52.299Z] [auth] Authentication Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-08-02T03:07:52.300Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":5504},{"address":"::1","family":"IPv6","port":5504}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"auth":[{"address":"127.0.0.1","family":"IPv4","port":5503}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-08-02T03:07:52.317Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-demo-project.json
