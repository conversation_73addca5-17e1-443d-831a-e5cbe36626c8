hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@apidevtools/json-schema-ref-parser@9.1.2':
    '@apidevtools/json-schema-ref-parser': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.1':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@electric-sql/pglite@0.2.17':
    '@electric-sql/pglite': private
  '@embedded-postgres/darwin-arm64@17.5.0-beta.15':
    '@embedded-postgres/darwin-arm64': private
  '@embedded-postgres/darwin-x64@17.5.0-beta.15':
    '@embedded-postgres/darwin-x64': private
  '@embedded-postgres/linux-arm64@17.5.0-beta.15':
    '@embedded-postgres/linux-arm64': private
  '@embedded-postgres/linux-arm@17.5.0-beta.15':
    '@embedded-postgres/linux-arm': private
  '@embedded-postgres/linux-ia32@17.5.0-beta.15':
    '@embedded-postgres/linux-ia32': private
  '@embedded-postgres/linux-ppc64@17.5.0-beta.15':
    '@embedded-postgres/linux-ppc64': private
  '@embedded-postgres/linux-x64@17.5.0-beta.15':
    '@embedded-postgres/linux-x64': private
  '@embedded-postgres/windows-x64@17.5.0-beta.15':
    '@embedded-postgres/windows-x64': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.27.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.27.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.1':
    '@eslint/plugin-kit': private
  '@firebase/ai@1.3.0(@firebase/app-types@0.9.3)(@firebase/app@0.13.0)':
    '@firebase/ai': private
  '@firebase/analytics-compat@0.2.22(@firebase/app-compat@0.4.0)(@firebase/app@0.13.0)':
    '@firebase/analytics-compat': private
  '@firebase/analytics-types@0.8.3':
    '@firebase/analytics-types': private
  '@firebase/analytics@0.10.16(@firebase/app@0.13.0)':
    '@firebase/analytics': private
  '@firebase/app-check-compat@0.3.25(@firebase/app-compat@0.4.0)(@firebase/app@0.13.0)':
    '@firebase/app-check-compat': private
  '@firebase/app-check-interop-types@0.3.3':
    '@firebase/app-check-interop-types': private
  '@firebase/app-check-types@0.5.3':
    '@firebase/app-check-types': private
  '@firebase/app-check@0.10.0(@firebase/app@0.13.0)':
    '@firebase/app-check': private
  '@firebase/app-compat@0.4.0':
    '@firebase/app-compat': private
  '@firebase/app-types@0.9.3':
    '@firebase/app-types': private
  '@firebase/app@0.13.0':
    '@firebase/app': private
  '@firebase/auth-compat@0.5.26(@firebase/app-compat@0.4.0)(@firebase/app-types@0.9.3)(@firebase/app@0.13.0)':
    '@firebase/auth-compat': private
  '@firebase/auth-interop-types@0.2.4':
    '@firebase/auth-interop-types': private
  '@firebase/auth-types@0.13.0(@firebase/app-types@0.9.3)(@firebase/util@1.12.0)':
    '@firebase/auth-types': private
  '@firebase/auth@1.10.6(@firebase/app@0.13.0)':
    '@firebase/auth': private
  '@firebase/component@0.6.17':
    '@firebase/component': private
  '@firebase/data-connect@0.3.9(@firebase/app@0.13.0)':
    '@firebase/data-connect': private
  '@firebase/database-compat@2.0.10':
    '@firebase/database-compat': private
  '@firebase/database-types@1.0.14':
    '@firebase/database-types': private
  '@firebase/database@1.0.19':
    '@firebase/database': private
  '@firebase/firestore-compat@0.3.51(@firebase/app-compat@0.4.0)(@firebase/app-types@0.9.3)(@firebase/app@0.13.0)':
    '@firebase/firestore-compat': private
  '@firebase/firestore-types@3.0.3(@firebase/app-types@0.9.3)(@firebase/util@1.12.0)':
    '@firebase/firestore-types': private
  '@firebase/firestore@4.7.16(@firebase/app@0.13.0)':
    '@firebase/firestore': private
  '@firebase/functions-compat@0.3.25(@firebase/app-compat@0.4.0)(@firebase/app@0.13.0)':
    '@firebase/functions-compat': private
  '@firebase/functions-types@0.6.3':
    '@firebase/functions-types': private
  '@firebase/functions@0.12.8(@firebase/app@0.13.0)':
    '@firebase/functions': private
  '@firebase/installations-compat@0.2.17(@firebase/app-compat@0.4.0)(@firebase/app-types@0.9.3)(@firebase/app@0.13.0)':
    '@firebase/installations-compat': private
  '@firebase/installations-types@0.5.3(@firebase/app-types@0.9.3)':
    '@firebase/installations-types': private
  '@firebase/installations@0.6.17(@firebase/app@0.13.0)':
    '@firebase/installations': private
  '@firebase/logger@0.4.4':
    '@firebase/logger': private
  '@firebase/messaging-compat@0.2.21(@firebase/app-compat@0.4.0)(@firebase/app@0.13.0)':
    '@firebase/messaging-compat': private
  '@firebase/messaging-interop-types@0.2.3':
    '@firebase/messaging-interop-types': private
  '@firebase/messaging@0.12.21(@firebase/app@0.13.0)':
    '@firebase/messaging': private
  '@firebase/performance-compat@0.2.19(@firebase/app-compat@0.4.0)(@firebase/app@0.13.0)':
    '@firebase/performance-compat': private
  '@firebase/performance-types@0.2.3':
    '@firebase/performance-types': private
  '@firebase/performance@0.7.6(@firebase/app@0.13.0)':
    '@firebase/performance': private
  '@firebase/remote-config-compat@0.2.17(@firebase/app-compat@0.4.0)(@firebase/app@0.13.0)':
    '@firebase/remote-config-compat': private
  '@firebase/remote-config-types@0.4.0':
    '@firebase/remote-config-types': private
  '@firebase/remote-config@0.6.4(@firebase/app@0.13.0)':
    '@firebase/remote-config': private
  '@firebase/storage-compat@0.3.22(@firebase/app-compat@0.4.0)(@firebase/app-types@0.9.3)(@firebase/app@0.13.0)':
    '@firebase/storage-compat': private
  '@firebase/storage-types@0.8.3(@firebase/app-types@0.9.3)(@firebase/util@1.12.0)':
    '@firebase/storage-types': private
  '@firebase/storage@0.13.12(@firebase/app@0.13.0)':
    '@firebase/storage': private
  '@firebase/util@1.12.0':
    '@firebase/util': private
  '@firebase/webchannel-wrapper@1.0.3':
    '@firebase/webchannel-wrapper': private
  '@floating-ui/core@1.7.0':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.0':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@google-cloud/cloud-sql-connector@1.8.1(encoding@0.1.13)':
    '@google-cloud/cloud-sql-connector': private
  '@google-cloud/paginator@5.0.2':
    '@google-cloud/paginator': private
  '@google-cloud/precise-date@4.0.0':
    '@google-cloud/precise-date': private
  '@google-cloud/projectify@4.0.0':
    '@google-cloud/projectify': private
  '@google-cloud/promisify@4.0.0':
    '@google-cloud/promisify': private
  '@google-cloud/pubsub@4.11.0(encoding@0.1.13)':
    '@google-cloud/pubsub': private
  '@googleapis/sqladmin@29.0.0(encoding@0.1.13)':
    '@googleapis/sqladmin': private
  '@grpc/grpc-js@1.9.15':
    '@grpc/grpc-js': private
  '@grpc/proto-loader@0.7.15':
    '@grpc/proto-loader': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@js-sdsl/ordered-map@4.4.2':
    '@js-sdsl/ordered-map': private
  '@jsdevtools/ono@7.1.3':
    '@jsdevtools/ono': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@npmcli/agent@3.0.0':
    '@npmcli/agent': private
  '@npmcli/fs@4.0.0':
    '@npmcli/fs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/semantic-conventions@1.30.0':
    '@opentelemetry/semantic-conventions': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pnpm/config.env-replace@1.1.0':
    '@pnpm/config.env-replace': private
  '@pnpm/network.ca-file@1.0.2':
    '@pnpm/network.ca-file': private
  '@pnpm/npm-conf@2.3.1':
    '@pnpm/npm-conf': private
  '@protobufjs/aspromise@1.1.2':
    '@protobufjs/aspromise': private
  '@protobufjs/base64@1.1.2':
    '@protobufjs/base64': private
  '@protobufjs/codegen@2.0.4':
    '@protobufjs/codegen': private
  '@protobufjs/eventemitter@1.1.0':
    '@protobufjs/eventemitter': private
  '@protobufjs/fetch@1.1.0':
    '@protobufjs/fetch': private
  '@protobufjs/float@1.0.2':
    '@protobufjs/float': private
  '@protobufjs/inquire@1.1.0':
    '@protobufjs/inquire': private
  '@protobufjs/path@1.1.2':
    '@protobufjs/path': private
  '@protobufjs/pool@1.1.0':
    '@protobufjs/pool': private
  '@protobufjs/utf8@1.1.0':
    '@protobufjs/utf8': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-avatar@1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-avatar': private
  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-label': private
  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-navigation-menu@1.2.13(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-navigation-menu': private
  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-select': private
  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slot@1.2.3(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-switch@1.2.5(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-switch': private
  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-is-hydrated': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rolldown/pluginutils@1.0.0-beta.9':
    '@rolldown/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@tailwindcss/node@4.1.7':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.7':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.7':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.7':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.7':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.7':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.7':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.7':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.7':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.7':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.7':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.7':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.7':
    '@tailwindcss/oxide': private
  '@tailwindcss/vite@4.1.7(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.19.4)(yaml@2.8.0))':
    '@tailwindcss/vite': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/caseless@0.12.5':
    '@types/caseless': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/long@4.0.2':
    '@types/long': private
  '@types/node@22.15.21':
    '@types/node': private
  '@types/react-dom@19.1.5(@types/react@19.1.5)':
    '@types/react-dom': private
  '@types/react@19.1.5':
    '@types/react': private
  '@types/request@2.48.12':
    '@types/request': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  '@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.32.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.32.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.32.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.32.1':
    '@typescript-eslint/visitor-keys': private
  '@vitejs/plugin-react@4.5.0(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.19.4)(yaml@2.8.0))':
    '@vitejs/plugin-react': private
  abbrev@3.0.1:
    abbrev: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  ajv-formats@3.0.1(ajv@8.17.1):
    ajv-formats: private
  ajv@6.12.6:
    ajv: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  archiver-utils@5.0.2:
    archiver-utils: private
  archiver@7.0.1:
    archiver: private
  argparse@1.0.10:
    argparse: private
  aria-hidden@1.2.6:
    aria-hidden: private
  array-flatten@1.1.1:
    array-flatten: private
  arrify@2.0.1:
    arrify: private
  as-array@2.0.0:
    as-array: private
  ast-types@0.13.4:
    ast-types: private
  async-exit-hook@2.0.1:
    async-exit-hook: private
  async-lock@1.4.1:
    async-lock: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  b4a@1.6.7:
    b4a: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  base64-js@1.5.1:
    base64-js: private
  basic-auth-connect@1.1.0:
    basic-auth-connect: private
  basic-auth@2.0.1:
    basic-auth: private
  basic-ftp@5.0.5:
    basic-ftp: private
  bignumber.js@9.3.0:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  body-parser@1.20.3:
    body-parser: private
  boxen@5.1.2:
    boxen: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.5:
    browserslist: private
  buffer-crc32@1.0.0:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer@5.7.1:
    buffer: private
  bytes@3.1.2:
    bytes: private
  cacache@19.0.1:
    cacache: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  call-me-maybe@1.0.2:
    call-me-maybe: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chardet@0.7.0:
    chardet: private
  chokidar@3.6.0:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  ci-info@2.0.0:
    ci-info: private
  cjson@0.3.3:
    cjson: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  cli-boxes@2.2.1:
    cli-boxes: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-highlight@2.1.11:
    cli-highlight: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-width@3.0.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone@1.0.4:
    clone: private
  clsx@2.1.1:
    clsx: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@3.2.1:
    color: private
  colorette@2.0.20:
    colorette: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@5.1.0:
    commander: private
  compress-commons@6.0.2:
    compress-commons: private
  compressible@2.0.18:
    compressible: private
  compression@1.8.0:
    compression: private
  concat-map@0.0.1:
    concat-map: private
  config-chain@1.1.13:
    config-chain: private
  configstore@5.0.1:
    configstore: private
  connect@3.7.0:
    connect: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  crc-32@1.2.2:
    crc-32: private
  crc32-stream@6.0.0:
    crc32-stream: private
  cross-env@7.0.3:
    cross-env: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  csstype@3.1.3:
    csstype: private
  csv-parse@5.6.0:
    csv-parse: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  database-server:
    database-server: private
  date-fns@2.30.0:
    date-fns: private
  debug@4.4.1:
    debug: private
  deep-equal-in-any-order@2.0.6:
    deep-equal-in-any-order: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-freeze@0.0.1:
    deep-freeze: private
  deep-is@0.1.4:
    deep-is: private
  defaults@1.0.4:
    defaults: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  discontinuous-range@1.0.0:
    discontinuous-range: private
  dot-prop@5.3.0:
    dot-prop: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexify@4.1.3:
    duplexify: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  electron-to-chromium@1.5.157:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojilib@2.4.0:
    emojilib: private
  enabled@2.0.0:
    enabled: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding@0.1.13:
    encoding: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  env-paths@2.2.1:
    env-paths: private
  environment@1.1.0:
    environment: private
  err-code@2.0.3:
    err-code: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.4:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-goat@2.1.1:
    escape-goat: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.27.0(jiti@2.4.2)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react-refresh@0.4.20(eslint@9.27.0(jiti@2.4.2)):
    eslint-plugin-react-refresh: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  eslint@9.27.0(jiti@2.4.2):
    eslint: private
  espree@10.3.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  events-listener@1.1.0:
    events-listener: private
  events@3.3.0:
    events: private
  exegesis-express@4.0.0:
    exegesis-express: private
  exegesis@4.3.0:
    exegesis: private
  exponential-backoff@3.1.2:
    exponential-backoff: private
  express@4.21.2:
    express: private
  extend@3.0.2:
    extend: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  faye-websocket@0.11.4:
    faye-websocket: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  fecha@4.2.3:
    fecha: private
  figures@3.2.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  filesize@6.4.0:
    filesize: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  firebase@11.8.1:
    firebase: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  fn.name@1.1.0:
    fn.name: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.2:
    form-data: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs-extra@10.1.0:
    fs-extra: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  fuzzy@0.1.3:
    fuzzy: private
  gaxios@6.7.1(encoding@0.1.13):
    gaxios: private
  gcp-metadata@6.1.1(encoding@0.1.13):
    gcp-metadata: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  get-uri@6.0.4:
    get-uri: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-slash@1.0.0:
    glob-slash: private
  glob-slasher@1.0.1:
    glob-slasher: private
  glob@10.4.5:
    glob: private
  global-dirs@3.0.1:
    global-dirs: private
  globals@16.2.0:
    globals: private
  google-auth-library@9.15.1(encoding@0.1.13):
    google-auth-library: private
  google-gax@4.6.1(encoding@0.1.13):
    google-gax: private
  google-logging-utils@0.0.2:
    google-logging-utils: private
  googleapis-common@7.2.0(encoding@0.1.13):
    googleapis-common: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gtoken@7.1.0(encoding@0.1.13):
    gtoken: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-yarn@2.1.0:
    has-yarn: private
  hasown@2.0.2:
    hasown: private
  heap-js@2.6.0:
    heap-js: private
  highlight.js@10.7.3:
    highlight.js: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http-parser-js@0.5.10:
    http-parser-js: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  iconv-lite@0.4.24:
    iconv-lite: private
  idb@7.1.1:
    idb: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-lazy@2.1.0:
    import-lazy: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inherits@2.0.4:
    inherits: private
  ini@2.0.0:
    ini: private
  inquirer-autocomplete-prompt@2.0.1(inquirer@8.2.6):
    inquirer-autocomplete-prompt: private
  inquirer@8.2.6:
    inquirer: private
  install-artifact-from-github@1.4.0:
    install-artifact-from-github: private
  ip-address@9.0.5:
    ip-address: private
  ip-regex@4.3.0:
    ip-regex: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-buffer@1.1.6:
    is-buffer: private
  is-ci@2.0.0:
    is-ci: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-installed-globally@0.4.0:
    is-installed-globally: private
  is-interactive@1.0.0:
    is-interactive: private
  is-npm@5.0.0:
    is-npm: private
  is-number@2.1.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-promise@4.0.0:
    is-promise: private
  is-stream-ended@0.1.4:
    is-stream-ended: private
  is-stream@2.0.1:
    is-stream: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-url@1.2.4:
    is-url: private
  is-wsl@1.1.0:
    is-wsl: private
  is-yarn-global@0.3.0:
    is-yarn-global: private
  is2@2.0.9:
    is2: private
  isarray@0.0.1:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isomorphic-fetch@3.0.0(encoding@0.1.13):
    isomorphic-fetch: private
  jackspeak@3.4.3:
    jackspeak: private
  jiti@2.4.2:
    jiti: private
  jju@1.4.0:
    jju: private
  join-path@1.1.1:
    join-path: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@3.14.1:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-bigint@1.0.0:
    json-bigint: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-helpfulerror@1.0.3:
    json-parse-helpfulerror: private
  json-ptr@3.1.1:
    json-ptr: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jws@4.0.0:
    jws: private
  keyv@4.5.4:
    keyv: private
  kind-of@3.2.2:
    kind-of: private
  kuler@2.0.0:
    kuler: private
  lazystream@1.0.1:
    lazystream: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  libsodium-wrappers@0.7.15:
    libsodium-wrappers: private
  libsodium@0.7.15:
    libsodium: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  locate-path@6.0.0:
    locate-path: private
  lodash._objecttypes@2.4.1:
    lodash._objecttypes: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isobject@2.4.1:
    lodash.isobject: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.mapvalues@4.6.0:
    lodash.mapvalues: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.snakecase@4.1.1:
    lodash.snakecase: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  logform@2.7.0:
    logform: private
  long@5.3.2:
    long: private
  lru-cache@7.18.3:
    lru-cache: private
  lsofi@1.0.0:
    lsofi: private
  lucide-react@0.511.0(react@19.1.0):
    lucide-react: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@3.1.0:
    make-dir: private
  make-fetch-happen@14.0.3:
    make-fetch-happen: private
  marked-terminal@7.3.0(marked@13.0.3):
    marked-terminal: private
  marked@13.0.3:
    marked: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass-collect@2.0.1:
    minipass-collect: private
  minipass-fetch@4.0.1:
    minipass-fetch: private
  minipass-flush@1.0.5:
    minipass-flush: private
  minipass-pipeline@1.2.4:
    minipass-pipeline: private
  minipass-sized@1.0.3:
    minipass-sized: private
  minipass@7.1.2:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  moo@0.5.2:
    moo: private
  morgan@1.10.0:
    morgan: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  mz@2.7.0:
    mz: private
  nan@2.22.2:
    nan: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  nearley@2.20.1:
    nearley: private
  negotiator@0.6.3:
    negotiator: private
  netmask@2.0.2:
    netmask: private
  next-themes@0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next-themes: private
  node-emoji@2.2.0:
    node-emoji: private
  node-fetch@2.7.0(encoding@0.1.13):
    node-fetch: private
  node-gyp@11.2.0:
    node-gyp: private
  node-releases@2.0.19:
    node-releases: private
  nopt@8.1.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  once@1.4.0:
    once: private
  one-time@1.0.0:
    one-time: private
  onetime@5.1.2:
    onetime: private
  open@6.4.0:
    open: private
  openapi3-ts@3.2.0:
    openapi3-ts: private
  optionator@0.9.4:
    optionator: private
  ora@5.4.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-defer@3.0.0:
    p-defer: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@7.0.3:
    p-map: private
  p-throttle@7.0.0:
    p-throttle: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parent-module@1.0.1:
    parent-module: private
  parse5-htmlparser2-tree-adapter@6.0.1:
    parse5-htmlparser2-tree-adapter: private
  parse5@5.1.1:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  pg-cloudflare@1.2.5:
    pg-cloudflare: private
  pg-connection-string@2.9.0:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.0(pg@8.16.0):
    pg-pool: private
  pg-protocol@1.10.0:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pg@8.16.0:
    pg: private
  pgpass@1.0.5:
    pgpass: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  portfinder@1.0.37:
    portfinder: private
  postcss@8.5.3:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  prelude-ls@1.2.1:
    prelude-ls: private
  proc-log@5.0.0:
    proc-log: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  progress@2.0.3:
    progress: private
  promise-breaker@6.0.0:
    promise-breaker: private
  promise-retry@2.0.1:
    promise-retry: private
  proto-list@1.2.4:
    proto-list: private
  proto3-json-serializer@2.0.2:
    proto3-json-serializer: private
  protobufjs@7.4.0:
    protobufjs: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  pupa@2.1.1:
    pupa: private
  qs@6.13.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  railroad-diagrams@1.0.0:
    railroad-diagrams: private
  randexp@0.4.6:
    randexp: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  re2@1.22.1:
    re2: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-hook-form@7.62.0(react@19.1.0):
    react-hook-form: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.5)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.7.0(@types/react@19.1.5)(react@19.1.0):
    react-remove-scroll: private
  react-router-dom@7.6.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-router-dom: private
  react-router@7.6.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-router: private
  react-style-singleton@2.2.3(@types/react@19.1.5)(react@19.1.0):
    react-style-singleton: private
  react@19.1.0:
    react: private
  readable-stream@4.7.0:
    readable-stream: private
  readdir-glob@1.1.3:
    readdir-glob: private
  readdirp@3.6.0:
    readdirp: private
  registry-auth-token@5.1.0:
    registry-auth-token: private
  registry-url@5.1.0:
    registry-url: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  restore-cursor@3.1.0:
    restore-cursor: private
  ret@0.1.15:
    ret: private
  retry-request@7.0.2(encoding@0.1.13):
    retry-request: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rollup@4.41.1:
    rollup: private
  router@2.2.0:
    router: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.26.0:
    scheduler: private
  semver-diff@3.1.1:
    semver-diff: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  server:
    server: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.2:
    shell-quote: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  skin-tone@2.0.0:
    skin-tone: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  sort-any@2.0.0:
    sort-any: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map@0.6.1:
    source-map: private
  spawn-command@0.0.2:
    spawn-command: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  sql-formatter@15.6.2:
    sql-formatter: private
  ssri@12.0.0:
    ssri: private
  stack-trace@0.0.10:
    stack-trace: private
  statuses@2.0.1:
    statuses: private
  stream-chain@2.2.5:
    stream-chain: private
  stream-events@1.0.5:
    stream-events: private
  stream-json@1.9.1:
    stream-json: private
  stream-shift@1.0.3:
    stream-shift: private
  streamx@2.22.0:
    streamx: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  stubs@3.0.0:
    stubs: private
  superstatic@9.2.0(encoding@0.1.13):
    superstatic: private
  supports-color@8.1.1:
    supports-color: private
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: private
  tailwind-merge@3.3.0:
    tailwind-merge: private
  tailwindcss@4.1.7:
    tailwindcss: private
  tapable@2.2.2:
    tapable: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@6.2.1:
    tar: private
  tcp-port-used@1.0.2:
    tcp-port-used: private
  teeny-request@9.0.0(encoding@0.1.13):
    teeny-request: private
  text-decoder@1.2.3:
    text-decoder: private
  text-hex@1.0.0:
    text-hex: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through2@2.0.5:
    through2: private
  through@2.3.8:
    through: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp@0.2.3:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  toxic@1.0.1:
    toxic: private
  tr46@0.0.3:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  triple-beam@1.4.1:
    triple-beam: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tslib@2.8.1:
    tslib: private
  tsscmp@1.0.6:
    tsscmp: private
  tsx@4.19.4:
    tsx: private
  tw-animate-css@1.3.0:
    tw-animate-css: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.21.3:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  typescript-eslint@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3):
    typescript-eslint: private
  typescript@5.8.3:
    typescript: private
  ui:
    ui: private
  undici-types@6.21.0:
    undici-types: private
  unicode-emoji-modifier-base@1.0.0:
    unicode-emoji-modifier-base: private
  unique-filename@4.0.0:
    unique-filename: private
  unique-slug@5.0.0:
    unique-slug: private
  unique-string@2.0.0:
    unique-string: private
  universal-analytics@0.5.3:
    universal-analytics: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  update-notifier-cjs@5.1.7(encoding@0.1.13):
    update-notifier-cjs: private
  uri-js@4.4.1:
    uri-js: private
  url-join@0.0.1:
    url-join: private
  url-template@2.0.8:
    url-template: private
  use-callback-ref@1.3.3(@types/react@19.1.5)(react@19.1.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.1.5)(react@19.1.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@8.3.2:
    uuid: private
  valid-url@1.0.9:
    valid-url: private
  vary@1.1.2:
    vary: private
  vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(tsx@4.19.4)(yaml@2.8.0):
    vite: private
  wcwidth@1.0.1:
    wcwidth: private
  web-vitals@4.2.4:
    web-vitals: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  websocket-driver@0.7.4:
    websocket-driver: private
  websocket-extensions@0.1.4:
    websocket-extensions: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which@2.0.2:
    which: private
  widest-line@3.1.0:
    widest-line: private
  winston-transport@4.9.0:
    winston-transport: private
  winston@3.17.0:
    winston: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@7.5.10:
    ws: private
  xdg-basedir@4.0.0:
    xdg-basedir: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zip-stream@6.0.1:
    zip-stream: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.9.0
pendingBuilds: []
prunedAt: Fri, 01 Aug 2025 23:38:53 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@cloudflare/workerd-darwin-64@1.20250508.0'
  - '@cloudflare/workerd-darwin-arm64@1.20250508.0'
  - '@cloudflare/workerd-linux-64@1.20250508.0'
  - '@cloudflare/workerd-linux-arm64@1.20250508.0'
  - '@embedded-postgres/darwin-arm64@17.5.0-beta.15'
  - '@embedded-postgres/darwin-x64@17.5.0-beta.15'
  - '@embedded-postgres/linux-arm64@17.5.0-beta.15'
  - '@embedded-postgres/linux-arm@17.5.0-beta.15'
  - '@embedded-postgres/linux-ia32@17.5.0-beta.15'
  - '@embedded-postgres/linux-ppc64@17.5.0-beta.15'
  - '@embedded-postgres/linux-x64@17.5.0-beta.15'
  - '@emnapi/runtime@1.4.3'
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-ia32@0.25.4'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-gnu@4.41.1'
  - '@rollup/rollup-linux-x64-musl@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@tailwindcss/oxide-android-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-x64@4.1.7'
  - '@tailwindcss/oxide-freebsd-x64@4.1.7'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.7'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.7'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.7'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\shop-bze\my-app\node_modules\.pnpm
virtualStoreDirMaxLength: 60
