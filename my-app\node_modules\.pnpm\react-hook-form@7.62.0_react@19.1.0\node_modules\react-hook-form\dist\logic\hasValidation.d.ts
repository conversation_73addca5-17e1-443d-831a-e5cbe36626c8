import type { Field } from '../types';
declare const _default: (options: Field["_f"]) => string | number | boolean | import("../types").ValidationValueMessage<boolean> | import("../types").ValidationValueMessage<string | number> | import("../types").ValidationRule<RegExp> | import("../types").Validate<any, import("../types").FieldValues> | Record<string, import("../types").Validate<any, import("../types").FieldValues>> | undefined;
export default _default;
//# sourceMappingURL=hasValidation.d.ts.map