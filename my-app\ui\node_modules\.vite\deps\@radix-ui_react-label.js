"use client";
import {
  Primitive
} from "./chunk-3SPUZT7U.js";
import "./chunk-KE7ZATTD.js";
import "./chunk-UU6TR3V6.js";
import {
  require_jsx_runtime
} from "./chunk-IHC2ALZA.js";
import {
  require_react
} from "./chunk-4H2SJVZJ.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// ../node_modules/.pnpm/@radix-ui+react-label@2.1.7_7793a902a18004088009a6b1964436da/node_modules/@radix-ui/react-label/dist/index.mjs
var React = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NAME = "Label";
var Label = React.forwardRef((props, forwardedRef) => {
  return (0, import_jsx_runtime.jsx)(
    Primitive.label,
    {
      ...props,
      ref: forwardedRef,
      onMouseDown: (event) => {
        var _a;
        const target = event.target;
        if (target.closest("button, input, select, textarea")) return;
        (_a = props.onMouseDown) == null ? void 0 : _a.call(props, event);
        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();
      }
    }
  );
});
Label.displayName = NAME;
var Root = Label;
export {
  Label,
  Root
};
//# sourceMappingURL=@radix-ui_react-label.js.map
